
> Configure project :
Fabric Loom: 1.10.5

> Task :compileJ<PERSON> UP-TO-DATE
> Task :processResources UP-TO-DATE
> Task :classes UP-TO-DATE
> Task :jar UP-TO-DATE
> Task :compileTestJava NO-SOURCE
> Task :processIncludeJars UP-TO-DATE
> Task :remapJar UP-TO-DATE
> Task :sourcesJar UP-TO-DATE
> Task :processTestResources NO-SOURCE
> Task :testClasses UP-TO-DATE
> Task :test NO-SOURCE
> Task :validateAccessWidener NO-SOURCE
> Task :check UP-TO-DATE
> Task :cleanRun
> Task :generateLog4jConfig UP-TO-DATE
> Task :generateRemapClasspath UP-TO-DATE
> Task :generateDLIConfig UP-TO-DATE
> Task :configureLaunch UP-TO-DATE
> Task :downloadAssets UP-TO-DATE
> Task :configureClientLaunch UP-TO-DATE
> Task :remapSourcesJar
> Task :assemble
> Task :build

> Task :runClient
[34m[22:51:43][m [32m[main/INFO][m [36m(FabricLoader/GameProvider)[m [0mLoading Minecraft 1.20.1 with Fabric Loader 0.16.13
[m[34m[22:51:43][m [32m[main/INFO][m [36m(FabricLoader)[m [0mLoading 59 mods:
	- fabric-api 0.92.5*****.1
	- fabric-api-base 0.4.32+1802ada577
	- fabric-api-lookup-api-v1 1.6.37+1802ada577
	- fabric-biome-api-v1 13.0.14+1802ada577
	- fabric-block-api-v1 1.0.12+1802ada577
	- fabric-block-view-api-v2 1.0.3+924f046a77
	- fabric-blockrenderlayer-v1 1.1.42+1802ada577
	- fabric-client-tags-api-v1 1.1.3+1802ada577
	- fabric-command-api-v1 1.2.35+f71b366f77
	- fabric-command-api-v2 2.2.14+1802ada577
	- fabric-commands-v0 0.2.52+df3654b377
	- fabric-containers-v0 0.1.66+df3654b377
	- fabric-content-registries-v0 4.0.13+1802ada577
	- fabric-convention-tags-v1 1.5.6+1802ada577
	- fabric-crash-report-info-v1 0.2.20+1802ada577
	- fabric-data-attachment-api-v1 1.0.2+de0fd6d177
	- fabric-data-generation-api-v1 12.3.6+1802ada577
	- fabric-dimensions-v1 2.1.55+1802ada577
	- fabric-entity-events-v1 1.6.1+1c78457f77
	- fabric-events-interaction-v0 0.6.4+13a40c6677
	- fabric-events-lifecycle-v0 0.2.64+df3654b377
	- fabric-game-rule-api-v1 1.0.41+1802ada577
	- fabric-gametest-api-v1 1.2.15+1802ada577
	- fabric-item-api-v1 2.1.29+1802ada577
	- fabric-item-group-api-v1 4.0.14+1802ada577
	- fabric-key-binding-api-v1 1.0.38+1802ada577
	- fabric-keybindings-v0 0.2.36+df3654b377
	- fabric-lifecycle-events-v1 2.2.23+1802ada577
	- fabric-loot-api-v2 1.2.3+1802ada577
	- fabric-loot-tables-v1 1.1.47+9e7660c677
	- fabric-message-api-v1 5.1.10+1802ada577
	- fabric-mining-level-api-v1 2.1.52+1802ada577
	- fabric-model-loading-api-v1 1.0.4+1802ada577
	- fabric-models-v0 0.4.3+9386d8a777
	- fabric-networking-api-v1 1.3.13+13a40c6677
	- fabric-networking-v0 0.3.53+df3654b377
	- fabric-object-builder-api-v1 11.1.5+e35120df77
	- fabric-particles-v1 1.1.3+1802ada577
	- fabric-recipe-api-v1 1.0.23+1802ada577
	- fabric-registry-sync-v0 2.3.5+1802ada577
	- fabric-renderer-api-v1 3.2.2+1802ada577
	- fabric-renderer-indigo 1.5.3+85287f9f77
	- fabric-renderer-registries-v1 3.2.47+df3654b377
	- fabric-rendering-data-attachment-v1 0.3.39+92a0d36777
	- fabric-rendering-fluids-v1 3.0.29+1802ada577
	- fabric-rendering-v0 1.1.50+df3654b377
	- fabric-rendering-v1 3.0.9+1802ada577
	- fabric-resource-conditions-api-v1 2.3.9+1802ada577
	- fabric-resource-loader-v0 0.11.12+fb82e9d777
	- fabric-screen-api-v1 2.0.9+1802ada577
	- fabric-screen-handler-api-v1 1.3.32+1802ada577
	- fabric-sound-api-v1 1.0.14+1802ada577
	- fabric-transfer-api-v1 3.3.6+8dd72ea377
	- fabric-transitive-access-wideners-v1 4.3.2+1802ada577
	- fabricloader 0.16.13
	- java 21
	- minecraft 1.20.1
	- mixinextras 0.4.1
	- pokecobbleclaim 1.0.0
[m[34m[22:51:43][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mSpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/6a12aacc794f1078458433116e9ed42c1cc98096/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[m[34m[22:51:43][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mLoaded Fabric development mappings for mixin remapper!
[m[34m[22:51:43][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mCompatibility level set to JAVA_17
[m[34m[22:51:45][m [32m[main/INFO][m [36m(FabricLoader/MixinExtras|Service)[m [0mInitializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[m[34m[22:51:51][m [32m[Datafixer Bootstrap/INFO][m [36m(Minecraft)[m [0m188 Datafixer optimizations took 146 milliseconds
[m[34m[22:51:52][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mEnvironment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mSetting user: Player858
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing PokeCobbleClaim mod
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering client-side network handlers
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering server-side network handlers
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mReset all town and player data versions
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mFound 3 individual town files, loading...
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99e80a89-582d-3fb3-8388-49270da696a6 in town aaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99e80a89-582d-3fb3-8388-49270da696a6 in town aaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaa' with 3 players from d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1de0626e-6c80-37c2-856b-2a8735302b74 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5bd62aa7-8886-32aa-953e-97bdbc2a6931 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8e13e2e6-c022-3506-be47-7e24287c9023 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e4e5b30f-b972-382c-b5e5-86c2e785a8a6 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34077235-b312-3a6b-9fdd-0d5e06f91c0e in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 7d246c97-157b-31d3-a8fc-0b0f5f437b63 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ecec10b-3fca-3e44-9b51-4cca88e1c207 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34692304-57b2-31f8-a84c-d22c6ff753f7 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d3ee42bb-6490-3bf6-992c-01b42b8a0039 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1c207d49-b6d4-301f-bf62-f591002e4f1a in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5bd62aa7-8886-32aa-953e-97bdbc2a6931 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1de0626e-6c80-37c2-856b-2a8735302b74 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1c207d49-b6d4-301f-bf62-f591002e4f1a in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34077235-b312-3a6b-9fdd-0d5e06f91c0e in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 7d246c97-157b-31d3-a8fc-0b0f5f437b63 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34692304-57b2-31f8-a84c-d22c6ff753f7 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ecec10b-3fca-3e44-9b51-4cca88e1c207 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e4e5b30f-b972-382c-b5e5-86c2e785a8a6 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d3ee42bb-6490-3bf6-992c-01b42b8a0039 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8e13e2e6-c022-3506-be47-7e24287c9023 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaaaaa' with 16 players from 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 2 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town aaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 2 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town aaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 2 towns to individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town aaaaaaaaa
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaa' with 1 players from c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored 20 player-town relationships
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded 3 towns from individual files
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown data loading completed. Final town count in TownManager: 3
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mPokeCobbleClaim mod initialized successfully
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing PokeCobbleClaim client
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering town keybinding
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering chunk boundary renderer
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded user preferences from: pokecobbleclaim-user-preferences.json
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized client-side data managers for synchronization
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering sounds
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully registered sound: pokecobbleclaim:notification.invite
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully registered sound: pokecobbleclaim:ui.button.click
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered sounds on client side
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing phone feature
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app position manager
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app position manager
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSetting up default app positions
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized 24 default app positions
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app registry
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app registry
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered 5 apps
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing phone texture manager
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing and registering phone notification overlay
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone notification overlay renderer
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone notification renderer
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered phone notification renderer
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone keybinding
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone keybinding
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized phone feature
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering Shape Visualizer Tool
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered shape visualizer tool
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mPokeCobbleClaim client initialized successfully
[m[34m[22:51:53][m [32m[Render thread/INFO][m [36m(Indigo)[m [0m[Indigo] Registering Indigo renderer!
[m[34m[22:51:54][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mBackend library: LWJGL version 3.3.2-snapshot
[m[34m[22:51:54][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0m[STDERR]: [LWJGL] [ThreadLocalUtil] Unsupported JNI version detected, this may result in a crash. Please inform LWJGL developers.
[m[34m[22:51:55][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mReloading ResourceManager: vanilla, fabric (fabric-rendering-data-attachment-v1, fabric-containers-v0, fabric-mining-level-api-v1, fabric-message-api-v1, fabric-screen-handler-api-v1, fabric-rendering-fluids-v1, fabric-object-builder-api-v1, fabric-transitive-access-wideners-v1, fabric-convention-tags-v1, fabric-data-generation-api-v1, fabric-commands-v0, fabric-dimensions-v1, fabric-rendering-v1, fabric-loot-tables-v1, fabric-registry-sync-v0, fabric-biome-api-v1, fabric-events-interaction-v0, fabric-renderer-registries-v1, fabricloader, fabric-entity-events-v1, fabric-gametest-api-v1, fabric-command-api-v2, fabric-crash-report-info-v1, fabric-key-binding-api-v1, pokecobbleclaim, fabric-api-lookup-api-v1, fabric-particles-v1, fabric-blockrenderlayer-v1, fabric-client-tags-api-v1, fabric-block-view-api-v2, fabric-content-registries-v0, fabric-keybindings-v0, fabric-models-v0, fabric-item-api-v1, fabric-transfer-api-v1, fabric-api-base, fabric-rendering-v0, fabric-networking-api-v1, fabric-game-rule-api-v1, fabric-data-attachment-api-v1, fabric-sound-api-v1, fabric-item-group-api-v1, fabric-lifecycle-events-v1, fabric-renderer-api-v1, fabric-loot-api-v2, fabric-command-api-v1, fabric-api, fabric-renderer-indigo, fabric-resource-conditions-api-v1, fabric-recipe-api-v1, fabric-model-loading-api-v1, fabric-block-api-v1, fabric-events-lifecycle-v0, fabric-networking-v0, fabric-resource-loader-v0, fabric-screen-api-v1)
[m[34m[22:51:55][m [32m[Worker-Main-12/INFO][m [36m(Minecraft)[m [0mFound unifont_all_no_pua-15.0.06.hex, loading
[m[34m[22:51:55][m [32m[Realms Notification Availability checker #1/INFO][m [36m(Minecraft)[m [0mCould not authorize you against Realms server: java.lang.RuntimeException: Failed to parse into SignedJWT: FabricMC
[m[34m[22:51:57][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mMissing sound for event: minecraft:item.goat_horn.play
[m[34m[22:51:57][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mMissing sound for event: minecraft:entity.goat.screaming.horn_break
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mOpenAL initialized on device Spector T6001
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mSound engine started
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[m[34m[22:51:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[m[34m[22:51:58][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mShader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[m[34m[22:51:58][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x0 minecraft:textures/atlas/particles.png-atlas
[m[34m[22:51:58][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[m[34m[22:51:58][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 128x128x0 minecraft:textures/atlas/mob_effects.png-atlas
[m[34m[22:52:41][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mConnecting to localhost, 25565
[m[34m[22:52:41][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town list response with 3 towns
[m[34m[22:52:41][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived towns: aaaaaaaaaaaaa, aaaaaaaaa, aaaaaaaaaa, 
[m[34m[22:52:41][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mUpdated town list cache (version 1, 3 towns)
[m[34m[22:52:41][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mTowns in cache: aaaaaaaaaaaaa, aaaaaaaaa, aaaaaaaaaa, 
[m[34m[22:52:41][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading positions for player: 651bbe0e-980e-38d7-8fbb-83ebfec85347
[m[34m[22:52:41][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mPositions file doesn't exist, using default positions
[m[34m[22:52:41][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized with default positions
[m[34m[22:52:41][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering client-side town commands
[m[34m[22:52:41][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClient-side town commands registered successfully
[m[34m[22:52:42][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mLoaded 2 advancements
[m[34m[22:52:45][m [32m[IO-Worker-16/INFO][m [36m(Minecraft)[m [0mSystem default audio device has changed!
[m[34m[22:52:45][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mMissing sound for event: minecraft:item.goat_horn.play
[m[34m[22:52:45][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mMissing sound for event: minecraft:entity.goat.screaming.horn_break
[m[34m[22:52:45][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mOpenAL initialized on device Sound Blaster GC7 Analog Stereo
[m[34m[22:52:45][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mSound engine started
[m[34m[22:53:02][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown key pressed, opening town screen
[m[34m[22:53:02][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mOpening town screen
[m[34m[22:53:02][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to server: pokecobbleclaim:town_list_request
[m[34m[22:53:03][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSending town join request for player Player858 to town d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[22:53:03][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown join request packet sent successfully
[m[34m[22:53:03][m [33m[Netty Epoll Client IO #0/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to server: pokecobbleclaim:town_data_request
[m[34m[22:53:03][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived player town membership: d42f4ef4-ff82-4347-a77c-d322040ff0b2 (version 4)
[m[34m[22:53:03][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived player town membership: d42f4ef4-ff82-4347-a77c-d322040ff0b2 (version 4)
[m[34m[22:53:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mModernTownScreen.selectCategory: playerTownId=d42f4ef4-ff82-4347-a77c-d322040ff0b2, playerTown=aaaaaaaaaa
[m[34m[22:53:04][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to server: pokecobbleclaim:town_data_request
[m[34m[22:53:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClick at (39,81) - Circle at (145,80) with radius 25
[m[34m[22:53:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClick is outside the circle
[m[34m[22:53:35][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClick at (311,239) - Circle at (145,80) with radius 25
[m[34m[22:53:35][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClick is outside the circle
[m[34m[22:53:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to server: pokecobbleclaim:town_list_request
[m